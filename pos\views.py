from datetime import datetime, timedelta
import json
import os
import zipfile
from pathlib import Path

from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.core.management import call_command
from django.core.cache import cache
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum, Prefetch
from django.http import JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django.views.decorators.cache import cache_page
from django.db.models.functions import ExtractDay, Cast
from django.db.models import F, IntegerField

from .models import Category, PaymentQRCode, Product, Sale, SaleItem# Authentication views
def login_view(request):
    # Redirect if already logged in
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        remember_me = request.POST.get('remember_me')

        if username and password:
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)

                # Set session expiry based on remember me
                if remember_me:
                    request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days
                else:
                    request.session.set_expiry(0)  # Browser session

                messages.success(request, f'Welcome back, {user.username}!')
                return redirect('dashboard')
            else:
                messages.error(request, 'Invalid username or password. Please try again.')
        else:
            messages.error(request, 'Please enter both username and password.')

    return render(request, 'auth/login.html')

@login_required
def logout_view(request):
    username = request.user.username
    logout(request)
    messages.success(request, f'Goodbye {username}! You have been successfully logged out.')
    return redirect('landing')

def landing_page(request):
    return render(request, 'landing.html')

# Dashboard views
@login_required
def dashboard(request):
    today = timezone.now().date()
    cache_key = f'dashboard_data_{today}'

    # Try to get cached data first
    context = cache.get(cache_key)
    if context is None:
        two_weeks_from_now = today + timedelta(days=14)

        # Optimize queries with single database hits
        dashboard_stats = Sale.objects.filter(payment_status=True).aggregate(
            total_sales=Sum('total_amount'),
            today_sales=Sum('total_amount', filter=Q(created_at__date=today))
        )

        product_stats = Product.objects.aggregate(
            total_products=Count('id'),
            low_stock_count=Count('id', filter=Q(stock_quantity__lte=10))
        )

        # Get products expiring within 2 weeks with optimized query
        near_expiry_products_query = Product.objects.filter(
            expiration_date__isnull=False,
            expiration_date__gte=today,
            expiration_date__lte=two_weeks_from_now
        ).select_related('category').order_by('expiration_date')[:10]  # Limit to 10

        # Calculate days until expiry in Python
        near_expiry_products = []
        for product in near_expiry_products_query:
            days_until_expiry = (product.expiration_date - today).days
            product.days_until_expiry = days_until_expiry
            near_expiry_products.append(product)

        # Optimized queries with select_related
        recent_sales = Sale.objects.select_related('cashier').order_by('-created_at')[:5]
        low_stock_products = Product.objects.select_related('category').filter(
            stock_quantity__lte=10
        )[:5]

        context = {
            'total_sales': dashboard_stats['total_sales'] or 0,
            'total_products': product_stats['total_products'] or 0,
            'today_sales': dashboard_stats['today_sales'] or 0,
            'low_stock_count': product_stats['low_stock_count'] or 0,
            'recent_sales': recent_sales,
            'low_stock_products': low_stock_products,
            'near_expiry_products': near_expiry_products,
        }

        # Cache for 5 minutes
        cache.set(cache_key, context, 300)

    return render(request, 'dashboard/index.html', context)

# Product views
@login_required
def product_list(request):
    # Optimize with select_related and increase pagination
    products_list = Product.objects.select_related('category').order_by('name')

    # Handle search with optimized query
    search_query = request.GET.get('search', '')
    if search_query:
        products_list = products_list.filter(
            Q(name__icontains=search_query) |
            Q(barcode__icontains=search_query) |
            Q(category__name__icontains=search_query)
        )

    # Increase pagination for better performance
    paginator = Paginator(products_list, 25)  # Show 25 products per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Cache categories for 10 minutes
    categories = cache.get('all_categories')
    if categories is None:
        categories = Category.objects.all().order_by('name')
        cache.set('all_categories', categories, 600)

    context = {
        'products': page_obj,
        'page_obj': page_obj,
        'categories': categories
    }
    return render(request, 'products/list.html', context)
@login_required
def add_product(request):
    if request.method == 'POST':
        try:
            expiration_date = request.POST.get('expiration_date')
            if not expiration_date:
                expiration_date = None
                
            product = Product.objects.create(
                name=request.POST['name'],
                barcode=request.POST['barcode'],
                category_id=request.POST['category'],
                price=request.POST['price'],
                stock_quantity=request.POST['stock_quantity'],
                description=request.POST['description'],
                expiration_date=expiration_date
            )
            messages.success(request, f'Product "{product.name}" added successfully!')
        except Exception as e:
            messages.error(request, f'Error adding product: {str(e)}')
    return redirect('products')

@login_required
def edit_product(request, product_id):
    if request.method == 'POST':
        try:
            product = Product.objects.get(id=product_id)
            product.name = request.POST['name']
            product.barcode = request.POST['barcode']
            product.category_id = request.POST['category']
            product.price = request.POST['price']
            product.stock_quantity = request.POST['stock_quantity']
            product.description = request.POST['description']
            
            expiration_date = request.POST.get('expiration_date')
            product.expiration_date = expiration_date if expiration_date else None
            
            product.save()
            messages.success(request, f'Product "{product.name}" updated successfully!')
        except Exception as e:
            messages.error(request, f'Error updating product: {str(e)}')
    return redirect('products')

@login_required
def delete_product(request, product_id):
    try:
        product = Product.objects.get(id=product_id)
        product_name = product.name
        product.delete()
        messages.success(request, f'Product "{product_name}" deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting product: {str(e)}')
    return redirect('products')

@login_required
def delete_all_products(request):
    if request.method == 'POST':
        try:
            product_count = Product.objects.count()
            if product_count == 0:
                messages.info(request, 'No products to delete.')
            else:
                Product.objects.all().delete()
                messages.success(request, f'Successfully deleted all {product_count} products!')
        except Exception as e:
            messages.error(request, f'Error deleting products: {str(e)}')
    return redirect('products')

# Category views
@login_required
def category_list(request):
    # Handle search
    search_query = request.GET.get('search', '')
    categories_list = Category.objects.select_related().annotate(
        product_count=Count('products')
    ).order_by('name')

    if search_query:
        categories_list = categories_list.filter(name__icontains=search_query)

    # Pagination for categories
    paginator = Paginator(categories_list, 12)  # Show 12 categories per page (3x4 grid)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'categories': page_obj,
        'page_obj': page_obj,
        'total_products': Product.objects.count(),
        'search_query': search_query,
    }
    return render(request, 'categories/list.html', context)

@login_required
def add_category(request):
    if request.method == 'POST':
        try:
            category = Category.objects.create(name=request.POST['name'])
            messages.success(request, f'Category "{category.name}" added successfully!')
        except Exception as e:
            messages.error(request, f'Error adding category: {str(e)}')
    return redirect('categories')

@login_required
def edit_category(request):
    if request.method == 'POST':
        try:
            category = Category.objects.get(id=request.POST['category_id'])
            category.name = request.POST['name']
            category.save()
            messages.success(request, f'Category "{category.name}" updated successfully!')
        except Exception as e:
            messages.error(request, f'Error updating category: {str(e)}')
    return redirect('categories')

@login_required
def delete_category(request, category_id):
    try:
        category = Category.objects.get(id=category_id)
        category.delete()
        messages.success(request, f'Category deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting category: {str(e)}')
    return redirect('categories')

@login_required
def delete_all_categories(request):
    if request.method == 'POST':
        try:
            category_count = Category.objects.count()
            if category_count == 0:
                messages.info(request, 'No categories to delete.')
            else:
                Category.objects.all().delete()
                messages.success(request, f'Successfully deleted all {category_count} categories!')
        except Exception as e:
            messages.error(request, f'Error deleting categories: {str(e)}')
    return redirect('categories')

# Sales views
@login_required
def sales_list(request):
    # Optimize with select_related and prefetch_related to include product information
    sales_queryset = Sale.objects.select_related('cashier').prefetch_related(
        'items__product'
    ).order_by('-created_at')

    # Add search functionality with optimized query including product names
    search_term = request.GET.get('search', '')
    if search_term:
        sales_queryset = sales_queryset.filter(
            Q(id__icontains=search_term) |
            Q(created_at__icontains=search_term) |
            Q(payment_reference__icontains=search_term) |
            Q(items__product__name__icontains=search_term)
        ).distinct()

    # Increase pagination for better performance
    paginator = Paginator(sales_queryset, 25)  # Show 25 sales per page
    page = request.GET.get('page')
    sales = paginator.get_page(page)

    return render(request, 'sales/list.html', {'sales': sales})

@login_required
def sale_details(request, sale_id):
    # Optimize with prefetch_related for sale items
    sale = get_object_or_404(
        Sale.objects.select_related('cashier').prefetch_related(
            Prefetch('items', queryset=SaleItem.objects.select_related('product__category'))
        ),
        id=sale_id
    )
    context = {
        'sale': sale
    }
    return render(request, 'sales/details.html', context)

def create_sale(request):
    # Load only first 20 products initially for better performance
    products = Product.objects.filter(stock_quantity__gt=0).select_related('category')[:20]
    qr_codes = PaymentQRCode.objects.filter(is_active=True)

    context = {
        'products': products,
        'qr_codes': qr_codes,
        'total_products': Product.objects.filter(stock_quantity__gt=0).count(),
    }
    return render(request, 'sales/create.html', context)

@require_POST
@transaction.atomic
def create_sale_api(request):
    try:
        data = json.loads(request.body)
        
        sale = Sale.objects.create(
            cashier=None,  # Remove the user requirement
            subtotal=float(data.get('subtotal', 0)),
            tax_amount=float(data.get('tax_amount', 0)),
            total_amount=float(data.get('total_amount', 0)),
            payment_reference=data['payment_reference'],
            payment_status=True
        )
        
        for item in data['items']:
            product = Product.objects.select_for_update().get(id=item['id'])
            quantity = int(item['quantity'])
            price = float(item['price'])
            
            if product.stock_quantity < quantity:
                raise ValueError(f'Insufficient stock for {product.name}')
            
            SaleItem.objects.create(
                sale=sale,
                product=product,
                quantity=quantity,
                price_at_sale=price
            )
            
            product.stock_quantity -= quantity
            product.save()
        
        return JsonResponse({'success': True, 'sale_id': sale.id})
        
    except Exception as e:
        transaction.set_rollback(True)
        return JsonResponse({'success': False, 'error': str(e)})
# Reports views
@login_required
def sales_report(request):
    today = timezone.now().date()
    sales = Sale.objects.filter(
        created_at__date=today
    ).select_related('cashier')
    
    context = {
        'sales': sales,
        'total_sales': sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0,
        'transaction_count': sales.count(),
        'average_sale': sales.aggregate(Avg('total_amount'))['total_amount__avg'] or 0,
        'total_tax': sales.aggregate(Sum('tax_amount'))['tax_amount__sum'] or 0,
        'report_date': today,
        'report_type': 'Daily Report'
    }
    return render(request, 'pos/sales_report.html', context)

@login_required
def sales_report_data(request):
    range_type = request.GET.get('range')
    today = timezone.now().date()
    
    if range_type == 'daily':
        start_date = today
        end_date = today
    elif range_type == 'monthly':
        start_date = today.replace(day=1)
        end_date = today
    elif range_type == 'yearly':
        start_date = today.replace(month=1, day=1)
        end_date = today
    else:
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()

    data = get_sales_data(start_date, end_date)
    return JsonResponse(data)


def get_sales_data(start_date, end_date):
    sales = Sale.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).select_related('cashier')

    aggregates = sales.aggregate(
        total_sales=Sum('total_amount'),
        total_tax=Sum('tax_amount'),
        transaction_count=Count('id'),
        average_sale=Avg('total_amount')
    )

    return {
        'sales': list(sales.values(
            'id', 'created_at', 'cashier__username',
            'subtotal', 'tax_amount', 'total_amount',
            'payment_status'
        )),
        'total_sales': float(aggregates['total_sales'] or 0),
        'total_tax': float(aggregates['total_tax'] or 0),
        'transaction_count': aggregates['transaction_count'] or 0,
        'average_sale': float(aggregates['average_sale'] or 0)
    }

# QR Code views
@login_required
def qr_codes(request):
    qr_codes = PaymentQRCode.objects.all()
    context = {
        'qr_codes': qr_codes,
    }
    return render(request, 'qr_codes/list.html', context)

from django.shortcuts import get_object_or_404

def get_receipt_data(request, sale_id):
    sale = get_object_or_404(Sale.objects.select_related('cashier'), id=sale_id)
    sale_items = SaleItem.objects.filter(sale=sale).select_related('product')

    receipt_data = {
        'id': sale.id,
        'created_at': sale.created_at.isoformat(),
        'subtotal': float(sale.subtotal),
        'tax_amount': float(sale.tax_amount),
        'total_amount': float(sale.total_amount),
        'payment_reference': sale.payment_reference,
        'cashier': sale.cashier.username if sale.cashier else 'System',
        'items': [{
            'product_name': item.product.name,
            'quantity': item.quantity,
            'price_at_sale': float(item.price_at_sale),
            'total': float(item.quantity * item.price_at_sale)
        } for item in sale_items]
    }

    return JsonResponse(receipt_data)

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json

@require_http_methods(["DELETE"])
def delete_sale(request, sale_id):
    try:
        sale = Sale.objects.get(id=sale_id)
        sale.delete()
        return JsonResponse({'message': 'Sale deleted successfully'}, status=200)
    except Sale.DoesNotExist:
        return JsonResponse({'error': 'Sale not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["POST"])
def bulk_delete_sales(request):
    try:
        data = json.loads(request.body)
        sale_ids = data.get('sale_ids', [])
        Sale.objects.filter(id__in=sale_ids).delete()
        return JsonResponse({'message': f'{len(sale_ids)} sales deleted successfully'}, status=200)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def delete_all_sales(request):
    if request.method == 'POST':
        try:
            sale_count = Sale.objects.count()
            if sale_count == 0:
                messages.info(request, 'No sales to delete.')
            else:
                Sale.objects.all().delete()
                messages.success(request, f'Successfully deleted all {sale_count} sales records!')
        except Exception as e:
            messages.error(request, f'Error deleting sales: {str(e)}')
    return redirect('sales')

from django.http import JsonResponse
from django.db.models import Q
from decimal import Decimal

@login_required
def search_products(request):
    search_term = request.GET.get('search', '')
    barcode = request.GET.get('barcode', '')
    category = request.GET.get('category', '')
    min_price = request.GET.get('min_price', '')
    max_price = request.GET.get('max_price', '')
    page = int(request.GET.get('page', 1))
    limit = int(request.GET.get('limit', 20))  # Default 20 products per page

    # Create cache key for this search
    cache_key = f'search_{hash(f"{search_term}_{barcode}_{category}_{min_price}_{max_price}_{page}_{limit}")}'

    # Try to get cached results first
    cached_result = cache.get(cache_key)
    if cached_result:
        return JsonResponse(cached_result)

    # Optimize base queryset with select_related and ordering
    queryset = Product.objects.filter(stock_quantity__gt=0).select_related('category').order_by('name')

    if search_term:
        queryset = queryset.filter(
            Q(name__icontains=search_term) |
            Q(description__icontains=search_term) |
            Q(barcode__icontains=search_term)
        )

    if barcode:
        queryset = queryset.filter(barcode=barcode)

    if category:
        queryset = queryset.filter(category__name=category)

    # Handle min_price with proper validation
    if min_price and min_price.strip():
        try:
            min_price_decimal = Decimal(min_price)
            queryset = queryset.filter(price__gte=min_price_decimal)
        except (ValueError, TypeError):
            pass  # Ignore invalid price values

    # Handle max_price with proper validation
    if max_price and max_price.strip():
        try:
            max_price_decimal = Decimal(max_price)
            queryset = queryset.filter(price__lte=max_price_decimal)
        except (ValueError, TypeError):
            pass  # Ignore invalid price values

    try:
        # Calculate pagination
        total_count = queryset.count()
        offset = (page - 1) * limit
        products_page = queryset[offset:offset + limit]

        products = [{
            'id': product.id,
            'name': product.name,
            'barcode': product.barcode,
            'price': str(product.price),
            'stock_quantity': product.stock_quantity,
            'category': product.category.name,
            'description': product.description
        } for product in products_page]

        result = {
            'products': products,
            'total_count': total_count,
            'page': page,
            'limit': limit,
            'has_more': offset + limit < total_count
        }

        # Cache the result for 2 minutes
        cache.set(cache_key, result, 120)

        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'products': [],
            'total_count': 0,
            'page': page,
            'limit': limit,
            'has_more': False
        }, status=500)

@login_required
def get_categories(request):
    categories = Category.objects.values_list('name', flat=True)
    return JsonResponse({'categories': list(categories)})

@login_required
def live_search_products(request):
    """Live search API for products with instant results"""
    search_term = request.GET.get('q', '').strip()
    category_filter = request.GET.get('category', '')
    limit = int(request.GET.get('limit', 10))  # Limit for live search

    if len(search_term) < 2:  # Minimum 2 characters for search
        return JsonResponse({'products': [], 'total_count': 0})

    # Create cache key for this search
    cache_key = f'live_search_{hash(f"{search_term}_{category_filter}_{limit}")}'

    # Try to get cached results first (shorter cache time for live search)
    cached_result = cache.get(cache_key)
    if cached_result:
        return JsonResponse(cached_result)

    # Optimize base queryset
    queryset = Product.objects.filter(stock_quantity__gt=0).select_related('category')

    # Apply search filter
    queryset = queryset.filter(
        Q(name__icontains=search_term) |
        Q(barcode__icontains=search_term) |
        Q(description__icontains=search_term)
    )

    # Apply category filter if provided
    if category_filter:
        queryset = queryset.filter(category__name=category_filter)

    # Limit results for live search
    products_page = queryset.order_by('name')[:limit]

    products = [{
        'id': product.id,
        'name': product.name,
        'barcode': product.barcode,
        'price': str(product.price),
        'stock_quantity': product.stock_quantity,
        'category': product.category.name,
        'description': product.description[:100] + '...' if len(product.description) > 100 else product.description
    } for product in products_page]

    result = {
        'products': products,
        'total_count': len(products),
        'search_term': search_term
    }

    # Cache the result for 30 seconds (shorter for live search)
    cache.set(cache_key, result, 30)

    return JsonResponse(result)

@login_required
def live_search_categories(request):
    """Live search API for categories"""
    search_term = request.GET.get('q', '').strip()
    limit = int(request.GET.get('limit', 10))

    if len(search_term) < 1:  # Minimum 1 character for category search
        return JsonResponse({'categories': [], 'total_count': 0})

    # Create cache key
    cache_key = f'live_search_cat_{hash(f"{search_term}_{limit}")}'

    # Try cache first
    cached_result = cache.get(cache_key)
    if cached_result:
        return JsonResponse(cached_result)

    # Search categories
    categories = Category.objects.filter(
        name__icontains=search_term
    ).annotate(
        product_count=Count('products')
    ).order_by('name')[:limit]

    categories_data = [{
        'id': category.id,
        'name': category.name,
        'product_count': category.product_count,
        'created_at': category.created_at.strftime('%Y-%m-%d')
    } for category in categories]

    result = {
        'categories': categories_data,
        'total_count': len(categories_data),
        'search_term': search_term
    }

    # Cache for 60 seconds
    cache.set(cache_key, result, 60)

    return JsonResponse(result)

# Backup views
@login_required
def backup_dashboard(request):
    """Display backup dashboard"""
    return render(request, 'dashboard/backup.html')

@login_required
def create_backup_api(request):
    """Create backup via API"""
    if request.method == 'POST':
        try:
            # Call the backup management command
            call_command('backup_database', '--compress')

            return JsonResponse({
                'success': True,
                'message': 'Backup created successfully!'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@login_required
def backup_status_api(request):
    """Get backup status information"""
    try:
        backup_dir = Path('backups')

        # Get backup files
        backup_files = []
        total_size = 0

        if backup_dir.exists():
            for file_path in backup_dir.iterdir():
                if file_path.is_file() and (file_path.suffix == '.zip' or file_path.name.startswith('pos_backup_')):
                    stat = file_path.stat()
                    size = stat.st_size
                    total_size += size

                    backup_files.append({
                        'name': file_path.name,
                        'size': format_file_size(size),
                        'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'type': 'compressed' if file_path.suffix == '.zip' else 'folder',
                        'path': str(file_path)
                    })
                elif file_path.is_dir() and file_path.name.startswith('pos_backup_'):
                    stat = file_path.stat()
                    try:
                        size = sum(f.stat().st_size for f in file_path.rglob('*') if f.is_file())
                    except:
                        size = 0
                    total_size += size

                    backup_files.append({
                        'name': file_path.name,
                        'size': format_file_size(size),
                        'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'type': 'folder',
                        'path': str(file_path)
                    })

        # Sort by date (newest first)
        backup_files.sort(key=lambda x: x['date'], reverse=True)

        # Get last backup info
        last_backup = backup_files[0] if backup_files else None

        return JsonResponse({
            'success': True,
            'last_backup': last_backup,
            'total_size': format_file_size(total_size),
            'backup_count': len(backup_files),
            'backups': backup_files[:10]  # Return only last 10 backups
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def download_backup(request, filename):
    """Download backup file"""
    try:
        backup_dir = Path('backups')
        file_path = backup_dir / filename

        if not file_path.exists():
            return JsonResponse({'error': 'Backup file not found'}, status=404)

        if file_path.is_file():
            with open(file_path, 'rb') as f:
                response = HttpResponse(f.read(), content_type='application/zip')
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
        else:
            return JsonResponse({'error': 'Cannot download directory'}, status=400)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def delete_backup(request, filename):
    """Delete backup file"""
    if request.method == 'POST':
        try:
            backup_dir = Path('backups')
            file_path = backup_dir / filename

            if not file_path.exists():
                return JsonResponse({'error': 'Backup file not found'}, status=404)

            if file_path.is_file():
                file_path.unlink()
            elif file_path.is_dir():
                import shutil
                shutil.rmtree(file_path)

            return JsonResponse({'success': True, 'message': f'Backup {filename} deleted successfully'})

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

@login_required
def restore_backup_api(request, filename):
    """Restore from backup file via API"""
    if request.method == 'POST':
        try:
            backup_dir = Path('backups')
            file_path = backup_dir / filename

            if not file_path.exists():
                return JsonResponse({'error': 'Backup file not found'}, status=404)

            # Call the restore management command
            call_command('restore_backup', str(file_path), '--confirm')

            return JsonResponse({
                'success': True,
                'message': f'System restored from backup: {filename}'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

    return JsonResponse({'error': 'Invalid request method'}, status=405)

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

@login_required
def load_more_products(request):
    """Load more products for infinite scrolling"""
    page = int(request.GET.get('page', 1))
    limit = int(request.GET.get('limit', 20))

    # Create cache key for pagination
    cache_key = f'load_more_products_{page}_{limit}'

    # Try to get cached results first
    cached_result = cache.get(cache_key)
    if cached_result:
        return JsonResponse(cached_result)

    try:
        # Optimize with select_related and ordering
        queryset = Product.objects.filter(stock_quantity__gt=0).select_related('category').order_by('name')

        # Calculate pagination
        total_count = queryset.count()
        offset = (page - 1) * limit
        products_page = queryset[offset:offset + limit]

        products = [{
            'id': product.id,
            'name': product.name,
            'barcode': product.barcode,
            'price': str(product.price),
            'stock_quantity': product.stock_quantity,
            'category': product.category.name,
            'description': product.description
        } for product in products_page]

        result = {
            'products': products,
            'total_count': total_count,
            'page': page,
            'limit': limit,
            'has_more': offset + limit < total_count
        }

        # Cache the result for 2 minutes
        cache.set(cache_key, result, 120)

        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'products': [],
            'total_count': 0,
            'page': page,
            'limit': limit,
            'has_more': False
        }, status=500)
