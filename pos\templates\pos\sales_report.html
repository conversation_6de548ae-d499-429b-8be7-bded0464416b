{% extends 'base/base.html' %}
{% load humanize %}

{% block title %}Advanced Sales Report - POS System{% endblock %}

{% block content %}
    <!-- Print Header (only visible when printing) -->
    <div class="print-only print-header">
        <h1>SALES REPORT</h1>
        <p>Generated on: <span id="printDate"></span></p>
        <p>Report Period: <span id="printPeriod"></span></p>
        <hr style="margin: 10px 0;">
    </div>

    <div class="bg-white shadow rounded-lg p-6">
        <!-- Enhanced Report Header -->
        <div class="mb-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-chart-line text-indigo-600 mr-3"></i>
                        Advanced Sales Report
                    </h1>
                    <p class="text-gray-600 mt-1">Comprehensive sales analytics and insights</p>
                </div>

                <!-- Report Actions -->
                <div class="flex flex-wrap gap-3 no-print">
                    <button onclick="printReport()" class="action-btn flex items-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-all duration-200">
                        <i class="fas fa-print mr-2"></i>Print Report
                    </button>
                    <button onclick="exportToPDF()" class="action-btn flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-all duration-200">
                        <i class="fas fa-file-pdf mr-2"></i>Export PDF
                    </button>
                    <button onclick="exportToExcel()" class="action-btn flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200">
                        <i class="fas fa-file-excel mr-2"></i>Export Excel
                    </button>
                    <button onclick="emailReport()" class="action-btn flex items-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-all duration-200">
                        <i class="fas fa-envelope mr-2"></i>Email Report
                    </button>
                </div>
            </div>
        </div>

    <!-- Date Range Selector -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="daily">
                <i class="far fa-calendar mr-2"></i>Today's Report
            </button>
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="monthly">
                <i class="far fa-calendar-alt mr-2"></i>Monthly Report
            </button>
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="yearly">
                <i class="far fa-calendar mr-2"></i>Yearly Report
            </button>
            <button class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" id="customRange">
                <i class="fas fa-calendar-plus mr-2"></i>Custom Range
            </button>
        </div>

    <!-- Custom Date Range Form -->
        <div id="customRangeForm" class="hidden mb-6 p-4 rounded bg-gray-100">
            <form class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Start Date</label>
                    <input type="date" name="start_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">End Date</label>
                    <input type="date" name="end_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Generate Report
                    </button>
                </div>
            </form>
        </div>

    <!-- Enhanced Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="summary-card bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium opacity-90">Total Sales</h3>
                        <p class="text-3xl font-bold total-sales">₱{{ total_sales|default:"0.00"|floatformat:2|intcomma }}</p>
                        <div class="flex items-center mt-2">
                            <span class="trend-indicator trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+12.5%
                            </span>
                            <span class="text-xs opacity-75 ml-2">vs last period</span>
                        </div>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>

            <div class="summary-card bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium opacity-90">Transactions</h3>
                        <p class="text-3xl font-bold transaction-count">{{ transaction_count|default:"0"|intcomma }}</p>
                        <div class="flex items-center mt-2">
                            <span class="trend-indicator trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+8.3%
                            </span>
                            <span class="text-xs opacity-75 ml-2">vs last period</span>
                        </div>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                </div>
            </div>

            <div class="summary-card bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-xl shadow-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium opacity-90">Average Sale</h3>
                        <p class="text-3xl font-bold average-sale">₱{{ average_sale|default:"0.00"|floatformat:2|intcomma }}</p>
                        <div class="flex items-center mt-2">
                            <span class="trend-indicator trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+3.7%
                            </span>
                            <span class="text-xs opacity-75 ml-2">vs last period</span>
                        </div>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                </div>
            </div>

            <div class="summary-card bg-gradient-to-br from-orange-500 to-orange-600 p-6 rounded-xl shadow-lg text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium opacity-90">Total Tax</h3>
                        <p class="text-3xl font-bold total-tax">₱{{ total_tax|default:"0.00"|floatformat:2|intcomma }}</p>
                        <div class="flex items-center mt-2">
                            <span class="trend-indicator trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+12.5%
                            </span>
                            <span class="text-xs opacity-75 ml-2">vs last period</span>
                        </div>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 no-print">
            <div class="bg-white p-6 rounded-xl shadow-lg border">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-indigo-600 mr-2"></i>Sales Trend
                </h3>
                <div class="chart-container">
                    <canvas id="salesTrendChart"></canvas>
                </div>
            </div>

            <div class="bg-white p-6 rounded-xl shadow-lg border">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-pie text-indigo-600 mr-2"></i>Sales by Category
                </h3>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>

    <!-- Sales Table -->
        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200" id="salesTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-calendar-alt mr-2"></i>Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-receipt mr-2"></i>Invoice #
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-box mr-2"></i>Products
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-calculator mr-2"></i>Subtotal
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-percentage mr-2"></i>Tax
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-money-bill-wave mr-2"></i>Total
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <i class="fas fa-check-circle mr-2"></i>Status
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for sale in sales %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">{{ sale.created_at|date:"Y-m-d H:i" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">#{{ sale.id }}</td>
                            <td class="px-6 py-4 text-gray-700">
                                <div class="max-w-xs">
                                    {% for item in sale.items.all %}
                                        <div class="text-sm">
                                            <span class="font-medium">{{ item.product.name }}</span>
                                            <span class="text-gray-500">({{ item.quantity }}x)</span>
                                        </div>
                                    {% empty %}
                                        <span class="text-gray-400 italic">No products</span>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.subtotal|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.tax_amount|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.total_amount|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if sale.payment_status %}
                                    <span class="sales-report-status paid">
                                        <i class="fas fa-check-circle mr-1"></i>Paid
                                    </span>
                                {% else %}
                                    <span class="sales-report-status pending">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

{% endblock %}
{% block extra_js %}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        let dataTable;
        let salesTrendChart;
        let categoryChart;

        $(document).ready(function() {
            initializeDataTable();
            initializeCharts();
            setupEventListeners();
        });

        function initializeDataTable() {
            dataTable = $('#salesTable').DataTable({
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true,
                dom: '<"flex flex-col md:flex-row justify-between items-center mb-4"Bf>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel mr-2"></i>Excel',
                        className: 'bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-all duration-200'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf mr-2"></i>PDF',
                        className: 'bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-all duration-200',
                        customize: function(doc) {
                            doc.content[1].table.widths = ['12%', '12%', '25%', '15%', '12%', '15%', '9%'];
                            doc.styles.tableHeader.fontSize = 10;
                            doc.defaultStyle.fontSize = 9;
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print mr-2"></i>Print',
                        className: 'bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200'
                    }
                ]
            });
        }

        function initializeCharts() {
            // Sales Trend Chart
            const salesCtx = document.getElementById('salesTrendChart').getContext('2d');
            salesTrendChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Sales (₱)',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₱' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Electronics', 'Clothing', 'Food', 'Books', 'Others'],
                    datasets: [{
                        data: [30, 25, 20, 15, 10],
                        backgroundColor: [
                            '#3B82F6',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444',
                            '#8B5CF6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function setupEventListeners() {
            // Date range buttons
            $('[data-range]').click(function() {
                const range = $(this).data('range');
                updatePrintPeriod(range);
                $.get('/sales/report/data/', { range: range }, updateReport);
            });

            // Custom range form
            $('#customRangeForm form').submit(function(e) {
                e.preventDefault();
                const startDate = $('input[name="start_date"]').val();
                const endDate = $('input[name="end_date"]').val();
                updatePrintPeriod('custom', startDate, endDate);
                $.get('/sales/report/data/', {
                    range: 'custom',
                    start_date: startDate,
                    end_date: endDate
                }, updateReport);
            });

            // Custom range toggle
            $('#customRange').click(function() {
                $('#customRangeForm').toggleClass('hidden');
            });
        }

        function updateReport(data) {
            // Update summary cards
            $('.total-sales').text('₱' + data.total_sales.toFixed(2));
            $('.transaction-count').text(data.transaction_count);
            $('.average-sale').text('₱' + data.average_sale.toFixed(2));
            $('.total-tax').text('₱' + data.total_tax.toFixed(2));

            // Update table
            dataTable.clear();
            data.sales.forEach(sale => {
                dataTable.row.add([
                    new Date(sale.created_at).toLocaleString(),
                    '#' + sale.id,
                    sale.products || 'No products',
                    '₱' + sale.subtotal.toFixed(2),
                    '₱' + sale.tax_amount.toFixed(2),
                    '₱' + sale.total_amount.toFixed(2),
                    sale.payment_status ?
                    '<span class="sales-report-status paid"><i class="fas fa-check-circle mr-1"></i>Paid</span>' :
                        '<span class="sales-report-status pending"><i class="fas fa-clock mr-1"></i>Pending</span>'
                ]);
            });
            dataTable.draw();

            // Update charts if data is available
            if (data.chart_data) {
                updateCharts(data.chart_data);
            }
        }

        function updateCharts(chartData) {
            // Update sales trend chart
            if (chartData.sales_trend) {
                salesTrendChart.data.labels = chartData.sales_trend.labels;
                salesTrendChart.data.datasets[0].data = chartData.sales_trend.data;
                salesTrendChart.update();
            }

            // Update category chart
            if (chartData.category_data) {
                categoryChart.data.labels = chartData.category_data.labels;
                categoryChart.data.datasets[0].data = chartData.category_data.data;
                categoryChart.update();
            }
        }

        function updatePrintPeriod(range, startDate = null, endDate = null) {
            let period = '';
            switch(range) {
                case 'daily':
                    period = 'Today';
                    break;
                case 'monthly':
                    period = 'This Month';
                    break;
                case 'yearly':
                    period = 'This Year';
                    break;
                case 'custom':
                    period = `${startDate} to ${endDate}`;
                    break;
                default:
                    period = 'All Time';
            }
            document.getElementById('printPeriod').textContent = period;
        }

        // Enhanced printing functionality
        function printReport() {
            document.getElementById('printDate').textContent = new Date().toLocaleString();
            window.print();
        }

        // Export functions
        function exportToPDF() {
            // Use DataTables PDF export
            dataTable.button('.buttons-pdf').trigger();
        }

        function exportToExcel() {
            // Use DataTables Excel export
            dataTable.button('.buttons-excel').trigger();
        }

        function emailReport() {
            const email = prompt('Enter email address to send the report:');
            if (email) {
                // Show loading state
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
                button.disabled = true;

                // Simulate email sending
                setTimeout(() => {
                    alert(`Sales report has been sent to ${email}`);
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 2000);
            }
        }

        // Auto-refresh functionality
        function enableAutoRefresh() {
            setInterval(() => {
                const activeRange = document.querySelector('[data-range].active')?.dataset.range || 'daily';
                $.get('/sales/report/data/', { range: activeRange }, updateReport);
            }, 30000); // Refresh every 30 seconds
        }

        // Initialize auto-refresh
        // enableAutoRefresh();
    </script>
{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body {
                font-size: 12px;
                color: #000;
                background: white;
            }
            .print-header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
            }
            .summary-card {
                border: 1px solid #000;
                margin-bottom: 10px;
                padding: 10px;
                background: #fff !important;
                color: #000 !important;
                page-break-inside: avoid;
            }
            .chart-container { display: none; }
            table {
                border-collapse: collapse;
                width: 100%;
            }
            th, td {
                border: 1px solid #000;
                padding: 5px;
                text-align: left;
            }
            th {
                background-color: #f0f0f0;
                font-weight: bold;
            }
        }

        .print-only { display: none; }

        .dataTables_wrapper {
            padding: 1rem;
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .dataTables_filter input {
            border: 2px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin-left: 0.5rem;
            transition: all 0.3s ease;
        }

        .dataTables_filter input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .dataTables_length select {
            border: 2px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .dataTables_paginate .paginate_button {
            padding: 0.75rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.5rem;
            background-color: #f8fafc;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .dataTables_paginate .paginate_button:hover {
            background-color: #e2e8f0;
            transform: translateY(-1px);
        }

        .dataTables_paginate .paginate_button.current {
            background-color: #3b82f6;
            color: white !important;
            border-color: #3b82f6;
        }

        .dataTables_paginate .paginate_button.current:hover {
            background-color: #2563eb;
        }

        .summary-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .summary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .chart-container {
            position: relative;
            height: 350px;
            margin: 20px 0;
        }

        .action-btn {
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .metric-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .trend-indicator {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-neutral { color: #6b7280; }

        /* DataTables Buttons Styling */
        .dt-buttons {
            margin-bottom: 1rem;
        }

        .dt-button {
            margin-right: 0.5rem !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
        }

        .dt-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }

        /* Enhanced table styling */
        #salesTable {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        #salesTable thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: #fff !important;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem;
            border: none;
            border-radius: 0;
            text-shadow: 0 1px 4px rgba(0,0,0,0.15);
        }

        #salesTable thead th:first-child {
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
        }

        #salesTable thead th:last-child {
            border-top-right-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
        }

        #salesTable tbody tr:hover {
            background-color: #f8fafc;
            transform: scale(1.005);
            transition: all 0.2s ease;
        }

        #salesTable tbody td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #e2e8f0;
        }

        /* Status badges for sales report */
        .sales-report-status {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sales-report-status.paid {
            background-color: #dcfce7;
            color: #166534;
        }

        .sales-report-status.pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            #salesTable thead th {
                padding: 0.75rem 0.5rem;
                font-size: 0.75rem;
            }

            #salesTable tbody td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .action-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .summary-card {
                margin-bottom: 1rem;
            }

            .chart-container {
                height: 250px;
            }
        }

        /* Loading animation */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
{% endblock %}
